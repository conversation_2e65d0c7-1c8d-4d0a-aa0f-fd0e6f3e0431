package com.cet.eem.fusion.transformer.core.controller;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.transformer.core.entity.bo.RadarChartInfo;
import com.cet.eem.fusion.transformer.core.entity.vo.EquipmentMonitorVo;
import com.cet.eem.fusion.transformer.core.entity.vo.LoadInfoVo;
import com.cet.eem.fusion.transformer.core.entity.vo.VoltageSideMonitorVo;
import com.cet.eem.fusion.transformer.core.service.TransformerAnalysisService;
import com.cet.eem.fusion.transformer.core.service.TransformerTaskService;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : TransformerAnalysisController
 * @Description : 变压器分析
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-07 10:24
 */
@Validated
@RestController
@Api(value = PluginInfoDef.Transformer.INTERFACE_PREFIX + "/v1/analysis", tags = "变压器分析接口")
@RequestMapping(value = PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX + "/v1/analysis")
public class TransformerAnalysisController {
    
    @Resource(name = "transformerAnalysis_transformerAnalysisService")
    private TransformerAnalysisService transformerAnalysisService;

    @Resource(name = "transformerAnalysis_transformerTaskService")
    private TransformerTaskService transformerTaskService;

    @ApiOperation("设备监测")
    @PostMapping("/baseInfo")
    public ApiResult<EquipmentMonitorVo> queryEquipmentMonitorInfo(@RequestParam @NotNull Long id) {
        return Result.ok(transformerAnalysisService.queryEquipmentMonitorInfo(id));
    }

    @ApiOperation("高低压侧电压数据")
    @PostMapping("/voltageSideMonitor")
    public ApiResult<List<VoltageSideMonitorVo>> queryVoltageSideMonitor(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryVoltageSideMonitor(id, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation("实时与历史负载信息")
    @PostMapping("/loadData")
    public ApiResult<LoadInfoVo> queryLoadInfo(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryLoadInfo(id, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation("雷达分析")
    @PostMapping("/radarChart")
    public ApiResult<RadarChartInfo> queryRadarChartInfo(@RequestParam @NotNull Long id) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryRadarChartInfo(id, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation("负载率趋势")
    @PostMapping("/loadCurve")
    public ApiResult<LoadRateVo> queryLoadRateTrend(@RequestBody LoadRateParam param) throws InstantiationException, IllegalAccessException {
        return Result.ok(transformerAnalysisService.queryLoadRateTrend(param, GlobalInfoUtils.getTenantId()));
    }

    @ApiOperation("计算历史信息")
    @PostMapping("/calHisData")
    public void task(@RequestParam @NotNull Long time) throws InstantiationException, IllegalAccessException {
        transformerTaskService.historicalLoadCalculation(time);
    }
}
