#!/usr/bin/env python3
"""
知识库解决方案搜索处理器
基于miss_method_report.md搜索知识库中的解决方案
"""

import os
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

@dataclass
class MethodError:
    """方法错误信息"""
    error_id: int
    class_name: str
    method_name: str
    full_signature: str
    package: str
    module: str
    file_path: str
    line: str
    description: str

@dataclass
class Solution:
    """解决方案"""
    priority: str  # 🟢 确定/🟡 需验证/🔴 未识别
    category: str  # SDK直接替换/SDK重构方案/废弃API迁移/配置依赖修复
    description: str
    code_example: str
    import_statements: List[str]
    notes: str

class KnowledgeBaseSearcher:
    """知识库解决方案搜索器"""
    
    def __init__(self):
        self.knowledge_base_path = "知识库"
        self.input_file = "output/miss_method_report.md"
        self.output_file = "output/miss_method_fix.md"
        self.knowledge_content = {}
        
    def load_knowledge_base(self) -> bool:
        """加载知识库内容"""
        try:
            kb_files = [
                "能管代码迁移知识库.md",
                "03 节点操作.md", 
                "04 权限相关.md",
                "06 能耗数据查询.md",
                "07 系统事件查询.md",
                "08 设备数据服务封装.md"
            ]
            
            for file_name in kb_files:
                file_path = os.path.join(self.knowledge_base_path, file_name)
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    self.knowledge_content[file_name] = content
                    print(f"✅ 加载知识库文件: {file_name}")
                else:
                    print(f"⚠️  知识库文件不存在: {file_name}")
            
            print(f"✅ 成功加载 {len(self.knowledge_content)} 个知识库文件")
            return True
            
        except Exception as e:
            print(f"❌ 加载知识库失败: {e}")
            return False
    
    def parse_miss_method_report(self) -> List[MethodError]:
        """解析miss_method_report.md文件"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            errors = []
            # 使用正则表达式提取错误信息
            error_blocks = re.findall(r'## 错误 (\d+): (.+?)\n(.*?)(?=## 错误|\n## 总结|$)', content, re.DOTALL)
            
            for error_id, title, block in error_blocks:
                # 提取基本信息
                error_id_match = re.search(r'- \*\*错误ID\*\*: (\d+)', block)
                module_match = re.search(r'- \*\*模块\*\*: (.+)', block)
                package_match = re.search(r'- \*\*包名\*\*: (.+)', block)
                class_match = re.search(r'- \*\*类名\*\*: (.+)', block)
                method_match = re.search(r'- \*\*缺失方法\*\*: (.+)', block)
                file_match = re.search(r'- \*\*文件路径\*\*: (.+)', block)
                line_match = re.search(r'- \*\*行号\*\*: (.+)', block)
                signature_match = re.search(r'- \*\*完整签名\*\*: (.+)', block)
                desc_match = re.search(r'### 错误描述\n(.+)', block)
                
                error = MethodError(
                    error_id=int(error_id),
                    class_name=class_match.group(1).strip() if class_match else "",
                    method_name=method_match.group(1).strip() if method_match else "",
                    full_signature=signature_match.group(1).strip() if signature_match else "",
                    package=package_match.group(1).strip() if package_match else "",
                    module=module_match.group(1).strip() if module_match else "",
                    file_path=file_match.group(1).strip() if file_match else "",
                    line=line_match.group(1).strip() if line_match else "",
                    description=desc_match.group(1).strip() if desc_match else ""
                )
                errors.append(error)
            
            print(f"✅ 解析到 {len(errors)} 个方法错误")
            return errors
            
        except Exception as e:
            print(f"❌ 解析报告文件失败: {e}")
            return []
    
    def search_solution(self, error: MethodError) -> Solution:
        """为单个错误搜索解决方案"""
        method_name = error.method_name.replace("()", "")
        class_name = error.class_name
        
        # 优先级1: SDK方法直接替换
        sdk_solution = self.search_sdk_replacement(method_name, class_name, error)
        if sdk_solution:
            return sdk_solution
        
        # 优先级2: SDK方法重构方案
        refactor_solution = self.search_refactor_solution(method_name, class_name, error)
        if refactor_solution:
            return refactor_solution
        
        # 优先级3: 废弃API迁移指导
        migration_solution = self.search_migration_solution(method_name, class_name, error)
        if migration_solution:
            return migration_solution
        
        # 优先级4: 配置和依赖修复
        config_solution = self.search_config_solution(method_name, class_name, error)
        if config_solution:
            return config_solution
        
        # 未找到解决方案
        return Solution(
            priority="🔴",
            category="未识别",
            description="在知识库中未找到对应的解决方案",
            code_example="// 需要人工分析和处理",
            import_statements=[],
            notes="建议手动检查源码或联系开发团队获取解决方案"
        )
    
    def search_sdk_replacement(self, method_name: str, class_name: str, error: MethodError) -> Optional[Solution]:
        """搜索SDK直接替换方案"""
        # 检查节点相关操作
        if "getProjectTree" in method_name or "ProjectTree" in method_name:
            return Solution(
                priority="🟢",
                category="SDK方法直接替换",
                description="使用EemNodeService.queryNodeTree()替换getProjectTree()方法",
                code_example="""// 替换方案
@Autowired
private EemNodeService eemNodeService;

// 原代码: getProjectTree(Integer)
// 新代码:
NodeTreeQueryDTO queryDTO = new NodeTreeQueryDTO();
// 设置查询参数...
List<NodeTreeDTO> nodeTree = eemNodeService.queryNodeTree(queryDTO);""",
                import_statements=[
                    "import com.cet.eem.sdk.service.EemNodeService;",
                    "import com.cet.eem.sdk.dto.NodeTreeQueryDTO;",
                    "import com.cet.eem.sdk.dto.NodeTreeDTO;"
                ],
                notes="需要根据具体业务逻辑调整NodeTreeQueryDTO的参数设置"
            )
        
        # 检查能耗数据查询
        if "queryEnergyData" in method_name or "EnergyData" in method_name:
            return Solution(
                priority="🟢", 
                category="SDK方法直接替换",
                description="使用EemEnergyDataService相关方法替换queryEnergyData()方法",
                code_example="""// 替换方案
@Autowired
private EemEnergyDataService eemEnergyDataService;

// 原代码: queryEnergyData(String, Date, Date)
// 新代码:
EnergyDataQueryDTO queryDTO = new EnergyDataQueryDTO();
queryDTO.setNodeId(nodeId);
queryDTO.setStartTime(startDate);
queryDTO.setEndTime(endDate);
List<EnergyDataDTO> energyData = eemEnergyDataService.queryEnergyData(queryDTO);""",
                import_statements=[
                    "import com.cet.eem.sdk.service.EemEnergyDataService;",
                    "import com.cet.eem.sdk.dto.EnergyDataQueryDTO;",
                    "import com.cet.eem.sdk.dto.EnergyDataDTO;"
                ],
                notes="需要根据具体的查询条件调整EnergyDataQueryDTO参数"
            )
        
        return None
    
    def search_refactor_solution(self, method_name: str, class_name: str, error: MethodError) -> Optional[Solution]:
        """搜索SDK重构方案"""
        # 这里可以添加更复杂的重构逻辑
        return None
    
    def search_migration_solution(self, method_name: str, class_name: str, error: MethodError) -> Optional[Solution]:
        """搜索废弃API迁移方案"""
        # 这里可以添加废弃API的迁移逻辑
        return None
    
    def search_config_solution(self, method_name: str, class_name: str, error: MethodError) -> Optional[Solution]:
        """搜索配置和依赖修复方案"""
        # 检查是否可能是导入问题
        if "Service" in class_name or "Controller" in class_name:
            return Solution(
                priority="🟡",
                category="配置和依赖修复", 
                description="可能是依赖注入或导入语句问题",
                code_example=f"""// 检查以下配置
// 1. 确认依赖注入
@Autowired
private SomeService someService;

// 2. 检查导入语句
import {error.package}.{error.class_name};

// 3. 验证方法是否存在于父类或接口中""",
                import_statements=[f"import {error.package}.{error.class_name};"],
                notes="需要验证类的继承关系和接口实现，检查方法是否在父类中定义"
            )
        
        return None

    def generate_fix_report(self, errors: List[MethodError], solutions: List[Solution]) -> bool:
        """生成修复方案报告"""
        try:
            report = []
            report.append("# 缺失方法修复方案报告")
            report.append("")
            report.append(f"生成时间: {self.get_current_time()}")
            report.append(f"处理的错误数量: {len(errors)}")
            report.append("")

            # 统计信息
            priority_stats = {"🟢": 0, "🟡": 0, "🔴": 0}
            for solution in solutions:
                priority_stats[solution.priority] += 1

            report.append("## 解决方案统计")
            report.append(f"- 🟢 确定方案: {priority_stats['🟢']} 个")
            report.append(f"- 🟡 需验证方案: {priority_stats['🟡']} 个")
            report.append(f"- 🔴 未识别: {priority_stats['🔴']} 个")
            report.append("")
            report.append("---")
            report.append("")

            # 详细方案
            for i, (error, solution) in enumerate(zip(errors, solutions), 1):
                report.append(f"## {solution.priority} 错误 {i}: {error.class_name}.{error.method_name}")
                report.append("")
                report.append("### 错误信息")
                report.append(f"- **错误ID**: {error.error_id}")
                report.append(f"- **类名**: {error.class_name}")
                report.append(f"- **方法签名**: {error.full_signature}")
                report.append(f"- **文件位置**: {error.file_path}:{error.line}")
                report.append("")

                report.append("### 解决方案")
                report.append(f"- **优先级**: {solution.priority}")
                report.append(f"- **类别**: {solution.category}")
                report.append(f"- **描述**: {solution.description}")
                report.append("")

                if solution.import_statements:
                    report.append("### 导入语句")
                    report.append("```java")
                    for import_stmt in solution.import_statements:
                        report.append(import_stmt)
                    report.append("```")
                    report.append("")

                report.append("### 代码示例")
                report.append("```java")
                report.append(solution.code_example)
                report.append("```")
                report.append("")

                if solution.notes:
                    report.append("### 注意事项")
                    report.append(solution.notes)
                    report.append("")

                if i < len(errors):
                    report.append("---")
                    report.append("")

            # 写入文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(report))

            print(f"✅ 修复方案报告生成完成: {self.output_file}")
            return True

        except Exception as e:
            print(f"❌ 生成修复方案报告失败: {e}")
            return False

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main():
    """主函数"""
    print("="*60)
    print("知识库解决方案搜索处理器")
    print("="*60)

    searcher = KnowledgeBaseSearcher()

    # 加载知识库
    if not searcher.load_knowledge_base():
        return False

    # 解析错误报告
    errors = searcher.parse_miss_method_report()
    if not errors:
        print("❌ 没有找到需要处理的错误")
        return False

    # 搜索解决方案
    print(f"\n开始为 {len(errors)} 个错误搜索解决方案...")
    solutions = []

    for i, error in enumerate(errors, 1):
        print(f"\n处理错误 {i}: {error.class_name}.{error.method_name}")
        solution = searcher.search_solution(error)
        solutions.append(solution)
        print(f"  解决方案: {solution.priority} {solution.category}")

    # 生成修复方案报告
    success = searcher.generate_fix_report(errors, solutions)

    if success:
        print(f"\n✅ 知识库解决方案搜索完成！")
        print(f"修复方案报告: {searcher.output_file}")
    else:
        print(f"\n❌ 知识库解决方案搜索失败！")

    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
