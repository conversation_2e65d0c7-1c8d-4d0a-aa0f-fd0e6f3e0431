# Java Error Analyzer 配置文件
# 所有路径支持相对路径和绝对路径，相对路径基于配置文件所在目录

# 项目配置
project:
  # 项目根目录路径
  root_path: "../"

  # Maven 模块列表 - 需要扫描的模块
  modules:
    - "eem-solution-group-energy-core"
    # 可以添加更多模块
    # - "eem-solution-group-energy-web"
    # - "eem-solution-group-energy-common"

  # 源码目录模式 (glob patterns)
  source_patterns:
    - "src/main/java/**/*.java"
    - "*/src/main/java/**/*.java"
    - "**/src/main/java/**/*.java"

  # 排除的目录模式
  exclude_patterns:
    - "**/target/**"
    - "**/build/**"
    - "**/.git/**"
    - "**/node_modules/**"

# IntelliJ IDEA 检查配置
inspection:
  # 检查配置文件路径
  profile_path: "./ai_method.xml"

  # 输出目录
  output_path: "./out"

  # 输出格式 (json, xml)
  format: "json"

  # IDEA 可执行文件路径 (可选，留空则自动查找)
  idea_executable: ""

# 处理配置
processing:
  # 超时时间 (秒)
  timeout_seconds: 30

  # 最大结果数量
  max_results: 10

  # 知识库文件路径
  knowledge_base_path: "知识库\能管代码迁移知识库.md"

  # 并发处理数量
  max_workers: 4

# 脚本路径配置
scripts:
  # 类名查找脚本
  class_finder: "./class_name_finder.py"

  # 源码和JAR包查找脚本
  source_jar_finder: "./FindNameFromJarAndSource.py"

# 日志配置
logging:
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"

  # 日志文件路径
  file: "error_analysis.log"

  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

  # 是否输出到控制台
  console: true

  # 日志文件最大大小 (MB)
  max_file_size: 10

  # 保留的日志文件数量
  backup_count: 5

# 错误分类配置
error_classification:
  # 分类规则 - 关键词映射
  patterns:
    import问题:
      keywords:
        - "Cannot resolve symbol"
        - "cannot find symbol"
        - "package does not exist"
        - "auth"
        - "aspect"
        - "EnumAndOr"
        - "OperationPermission"
        - "OperationLog"
        - "bll"
        - "common"
        - "log"
        - "annotation"
        - "constant"
      priority: 1
      color: "🟢"
      description: "高优先级 - 基础依赖问题"

    返回类型问题:
      keywords:
        - "Result"
        - "ResultWithTotal"
        - "ResponseEntity"
        - "Response"
      priority: 2
      color: "🟡"
      description: "中优先级 - 接口统一"

    废弃服务问题:
      keywords:
        - "NodeService"
        - "NodeAuthCheckService"
        - "EemCloudAuthService"
        - "QuantityObjectDao"
        - "EnergySupplyDao"
        - "AuthUtils"
      priority: 6
      color: "🔴"
      description: "低优先级 - 复杂重构"

    工具类问题:
      keywords:
        - "CommonUtils"
        - "JsonUtil"
        - "DateUtils"
        - "MessagePushUtils"
      priority: 3
      color: "🟡"
      description: "中优先级 - 工具类替换"

    权限相关问题:
      keywords:
        - "permission"
        - "auth"
        - "Permission"
        - "Auth"
      priority: 5
      color: "🟡"
      description: "中优先级 - 权限系统"

    注解问题:
      keywords:
        - "@Resource"
        - "@Autowired"
        - "@Component"
        - "@Service"
      priority: 4
      color: "🟡"
      description: "中优先级 - 依赖注入"

# 输出配置
output:
  # 问题列表文件名
  problem_list_file: "问题列表.md"

  # 任务列表文件名
  task_list_file: "task.md"

  # 输出目录
  output_dir: "./out"

  # 是否生成详细报告
  detailed_report: true

  # 报告语言 (zh, en)
  language: "zh"

# Maven 配置
maven:
  # Maven 可执行文件路径 (可选，留空则使用系统PATH)
  executable: ""

  # Maven 参数
  args:
    - "-q" # 安静模式
    - "-DskipTests" # 跳过测试

  # 构建超时时间 (秒)
  build_timeout: 300

# 环境变量映射 (用于覆盖配置)
env_overrides:
  JAVA_ANALYZER_PROJECT_PATH: "project.root_path"
  JAVA_ANALYZER_TIMEOUT: "processing.timeout_seconds"
  JAVA_ANALYZER_MAX_RESULTS: "processing.max_results"
  JAVA_ANALYZER_LOG_LEVEL: "logging.level"
  JAVA_ANALYZER_LOG_FILE: "logging.file"
  JAVA_ANALYZER_KNOWLEDGE_BASE: "processing.knowledge_base_path"
