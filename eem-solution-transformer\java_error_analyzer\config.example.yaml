# Java Error Analyzer 配置示例
# 复制此文件为 config.yaml 并根据你的项目修改

# 项目配置 - 根据你的实际项目修改
project:
  # 项目根目录路径 (相对于此配置文件)
  root_path: "../your-project-root"
  
  # Maven 模块列表 - 需要扫描的模块
  modules:
    - "your-module-name"
    # 添加更多模块
    # - "another-module"
  
  # 源码目录模式 (通常不需要修改)
  source_patterns:
    - "src/main/java/**/*.java"
    - "*/src/main/java/**/*.java"
    - "**/src/main/java/**/*.java"

# IntelliJ IDEA 检查配置
inspection:
  # 检查配置文件路径
  profile_path: "./ai_method.xml"
  
  # 输出目录
  output_path: "./out"
  
  # IDEA 可执行文件路径 (可选，留空则自动查找)
  idea_executable: ""

# 处理配置
processing:
  # 超时时间 (秒)
  timeout_seconds: 60
  
  # 最大结果数量
  max_results: 20
  
  # 知识库文件路径
  knowledge_base_path: "./能管代码迁移知识库.md"

# 日志配置
logging:
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"
  
  # 日志文件路径
  file: "error_analysis.log"

# 输出配置
output:
  # 输出目录
  output_dir: "./out"
  
  # 问题列表文件名
  problem_list_file: "问题列表.md"
  
  # 任务列表文件名
  task_list_file: "task.md"