"""
Java Error Analyzer 配置管理工具
支持 YAML 和 JSON 配置文件，环境变量覆盖
"""
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from models import ProcessingConfig

try:
    import yaml
    HAS_YAML = True
except ImportError:
    HAS_YAML = False


class ConfigManager:
    """配置管理器 - 支持 YAML/JSON 配置文件和环境变量覆盖"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，默认查找 config.yaml 或 config.json
        """
        self.config_file = config_file or self._find_config_file()
        self.config_data = {}
        self.config_dir = Path(__file__).parent  # 配置文件所在目录
        self._load_config()
    
    def _find_config_file(self) -> Optional[str]:
        """自动查找配置文件"""
        config_dir = Path(__file__).parent
        
        # 按优先级查找配置文件
        candidates = [
            config_dir / "config.yaml",
            config_dir / "config.yml", 
            config_dir / "config.json"
        ]
        
        for config_path in candidates:
            if config_path.exists():
                return str(config_path)
        
        return None
    
    def _load_config(self) -> None:
        """加载配置文件"""
        # 获取默认配置
        self.config_data = self._get_default_config()
        
        # 如果找到配置文件，加载并合并
        if self.config_file and Path(self.config_file).exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if (self.config_file.endswith('.yaml') or self.config_file.endswith('.yml')) and HAS_YAML:
                        file_config = yaml.safe_load(f)
                    else:
                        file_config = json.load(f)
                
                # 合并配置
                if file_config:
                    self._merge_config(self.config_data, file_config)
                    
            except Exception as e:
                print(f"警告: 无法加载配置文件 {self.config_file}: {e}")
        else:
            print(f"未找到配置文件，使用默认配置")
        
        # 处理相对路径
        self._resolve_relative_paths()
        
        # 环境变量覆盖
        self._apply_env_overrides()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "project": {
                "root_path": "../",
                "modules": ["eem-solution-group-energy-core"],
                "source_patterns": [
                    "src/main/java/**/*.java",
                    "*/src/main/java/**/*.java", 
                    "**/src/main/java/**/*.java"
                ],
                "exclude_patterns": [
                    "**/target/**",
                    "**/build/**",
                    "**/.git/**"
                ]
            },
            "inspection": {
                "profile_path": "./ai_method.xml",
                "output_path": "./out",
                "format": "json",
                "idea_executable": ""
            },
            "processing": {
                "timeout_seconds": 30,
                "max_results": 10,
                "knowledge_base_path": "./能管代码迁移知识库.md",
                "max_workers": 4
            },
            "scripts": {
                "class_finder": "./FindNameFromJarAndSource.py",
                "source_jar_finder": "./FindNameFromJarAndSource.py"
            },
            "logging": {
                "level": "INFO",
                "file": "error_analysis.log",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "console": True
            },
            "output": {
                "problem_list_file": "问题列表.md",
                "task_list_file": "task.md",
                "output_dir": "./out"
            }
        }
    
    def _resolve_relative_paths(self) -> None:
        """解析相对路径为绝对路径"""
        path_keys = [
            ("project", "root_path"),
            ("inspection", "profile_path"),
            ("inspection", "output_path"),
            ("processing", "knowledge_base_path"),
            ("scripts", "class_finder"),
            ("scripts", "source_jar_finder"),
            ("logging", "file"),
            ("output", "output_dir")
        ]
        
        for section, key in path_keys:
            if section in self.config_data and key in self.config_data[section]:
                path_value = self.config_data[section][key]
                if path_value and not os.path.isabs(path_value):
                    # 相对于配置文件目录的路径
                    abs_path = self.config_dir / path_value
                    self.config_data[section][key] = str(abs_path.resolve())
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> None:
        """深度合并配置字典"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        # 从配置文件中获取环境变量映射，如果没有则使用默认映射
        env_mappings = self.config_data.get("env_overrides", {
            "JAVA_ANALYZER_PROJECT_PATH": "project.root_path",
            "JAVA_ANALYZER_TIMEOUT": "processing.timeout_seconds",
            "JAVA_ANALYZER_MAX_RESULTS": "processing.max_results",
            "JAVA_ANALYZER_LOG_LEVEL": "logging.level",
            "JAVA_ANALYZER_LOG_FILE": "logging.file",
            "JAVA_ANALYZER_KNOWLEDGE_BASE": "processing.knowledge_base_path"
        })
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self._set_nested_value(config_path, value)
    
    def _set_nested_value(self, path: str, value: str) -> None:
        """设置嵌套配置值"""
        keys = path.split('.')
        current = self.config_data
        
        # 导航到最后一级的父级
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 设置最终值，进行类型转换
        final_key = keys[-1]
        if final_key in ["timeout_seconds", "max_results", "max_workers"]:
            try:
                value = int(value)
            except ValueError:
                return
        elif final_key in ["console"]:
            value = value.lower() in ('true', '1', 'yes', 'on')
        
        current[final_key] = value
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            default: 默认值
        
        Returns:
            配置值
        """
        return self.config_data.get(section, {}).get(key, default)
    
    def get_nested(self, path: str, default: Any = None) -> Any:
        """
        获取嵌套配置值
        
        Args:
            path: 配置路径，如 "project.root_path"
            default: 默认值
        
        Returns:
            配置值
        """
        keys = path.split('.')
        current = self.config_data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return current
    
    def get_project_config(self) -> Dict[str, Any]:
        """获取项目配置"""
        return self.config_data.get("project", {})
    
    def get_inspection_config(self) -> Dict[str, Any]:
        """获取检查配置"""
        return self.config_data.get("inspection", {})
    
    def get_processing_config(self) -> ProcessingConfig:
        """
        获取处理配置对象
        
        Returns:
            ProcessingConfig实例
        """
        project = self.config_data.get("project", {})
        processing = self.config_data.get("processing", {})
        scripts = self.config_data.get("scripts", {})
        logging_config = self.config_data.get("logging", {})
        
        return ProcessingConfig(
            script_path=scripts.get("class_finder", "./FindNameFromJarAndSource.py"),
            project_path=project.get("root_path", "../"),
            timeout_seconds=processing.get("timeout_seconds", 30),
            max_results=processing.get("max_results", 10),
            knowledge_base_path=processing.get("knowledge_base_path", "./能管代码迁移知识库.md"),
            log_level=logging_config.get("level", "INFO"),
            log_file=logging_config.get("file", "error_analysis.log")
        )
    
    def get_modules(self) -> List[str]:
        """获取要扫描的模块列表"""
        return self.get_nested("project.modules", ["eem-solution-group-energy-core"])
    
    def get_project_root(self) -> str:
        """获取项目根目录"""
        return self.get_nested("project.root_path", "../")
    
    def get_source_patterns(self) -> List[str]:
        """获取源码扫描模式"""
        return self.get_nested("project.source_patterns", [
            "src/main/java/**/*.java",
            "*/src/main/java/**/*.java",
            "**/src/main/java/**/*.java"
        ])
    
    def get_exclude_patterns(self) -> List[str]:
        """获取排除模式"""
        return self.get_nested("project.exclude_patterns", [
            "**/target/**",
            "**/build/**",
            "**/.git/**"
        ])
    
    def get_error_classification_patterns(self) -> Dict[str, Any]:
        """获取错误分类模式"""
        return self.get_nested("error_classification.patterns", {})
    
    def print_config_summary(self) -> None:
        """打印配置摘要"""
        print("=== Java Error Analyzer 配置摘要 ===")
        print(f"配置文件: {self.config_file or '使用默认配置'}")
        print(f"项目根目录: {self.get_project_root()}")
        print(f"扫描模块: {', '.join(self.get_modules())}")
        print(f"日志级别: {self.get('logging', 'level')}")
        print(f"输出目录: {self.get('output', 'output_dir')}")
        print("=" * 40)


# 为了向后兼容，保留原来的类名
SimpleConfigManager = ConfigManager