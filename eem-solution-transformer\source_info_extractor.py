#!/usr/bin/env python3
"""
缺失方法源码信息获取处理器
调用source-context-extractor工具处理miss_method.json
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any

class SourceInfoExtractor:
    """源码信息提取器"""
    
    def __init__(self):
        self.input_file = "output/miss_method.json"
        self.output_file = "output/miss_method_report.md"
        self.extractor_path = "source_context_extractor"
    
    def check_input_file(self) -> bool:
        """检查输入文件是否存在"""
        if not os.path.exists(self.input_file):
            print(f"❌ 输入文件不存在: {self.input_file}")
            return False
        
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data:
                print(f"⚠️  输入文件为空: {self.input_file}")
                return False
            
            print(f"✅ 输入文件检查通过: {len(data)} 个缺失方法错误")
            return True
            
        except Exception as e:
            print(f"❌ 输入文件格式错误: {e}")
            return False
    
    def call_source_context_extractor(self) -> bool:
        """调用source-context-extractor工具"""
        try:
            print("🔄 调用 source-context-extractor 工具...")
            
            # 构建命令
            cmd = [
                sys.executable, "-m", "source_context_extractor.main",
                "-i", self.input_file,
                "-o", self.output_file
            ]
            
            print(f"执行命令: {' '.join(cmd)}")
            
            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                cwd=os.getcwd()
            )
            
            if result.returncode == 0:
                print("✅ source-context-extractor 执行成功")
                print("输出信息:")
                if result.stdout:
                    print(result.stdout)
                return True
            else:
                print(f"❌ source-context-extractor 执行失败 (返回码: {result.returncode})")
                print("错误信息:")
                if result.stderr:
                    print(result.stderr)
                if result.stdout:
                    print("标准输出:")
                    print(result.stdout)
                return False
                
        except Exception as e:
            print(f"❌ 调用 source-context-extractor 时发生异常: {e}")
            return False
    
    def generate_fallback_report(self) -> bool:
        """生成备用报告（当source-context-extractor失败时）"""
        try:
            print("🔄 生成备用源码信息报告...")
            
            # 读取miss_method.json
            with open(self.input_file, 'r', encoding='utf-8') as f:
                errors = json.load(f)
            
            # 生成Markdown报告
            report_content = self.create_manual_report(errors)
            
            # 写入报告文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"✅ 备用报告生成完成: {self.output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 生成备用报告失败: {e}")
            return False
    
    def create_manual_report(self, errors: List[Dict[str, Any]]) -> str:
        """创建手动分析报告"""
        report = []
        report.append("# 缺失方法源码信息分析报告")
        report.append("")
        report.append(f"生成时间: {self.get_current_time()}")
        report.append(f"分析的错误数量: {len(errors)}")
        report.append("")
        report.append("---")
        report.append("")
        
        for i, error in enumerate(errors, 1):
            report.append(f"## 错误 {i}: {error.get('class', 'Unknown')}.{error.get('missing_method', 'Unknown')}")
            report.append("")
            report.append("### 基本信息")
            report.append(f"- **错误ID**: {error.get('issue_id', 'N/A')}")
            report.append(f"- **模块**: {error.get('module', 'N/A')}")
            report.append(f"- **包名**: {error.get('package', 'N/A')}")
            report.append(f"- **类名**: {error.get('class', 'N/A')}")
            report.append(f"- **缺失方法**: {error.get('missing_method', 'N/A')}")
            report.append(f"- **文件路径**: {error.get('file_path', 'N/A')}")
            report.append(f"- **行号**: {error.get('line', 'N/A')}")
            report.append("")
            
            report.append("### 错误描述")
            report.append(f"{error.get('description', 'N/A')}")
            report.append("")
            
            report.append("### 源码上下文分析")
            report.append("**注意**: 由于source-context-extractor工具调用失败，以下为基于错误信息的初步分析：")
            report.append("")
            
            # 分析方法签名
            method_name = error.get('missing_method', '')
            description = error.get('description', '')
            
            if '(' in method_name and ')' in method_name:
                report.append(f"- **方法签名**: {method_name}")
            
            if '(' in description and ')' in description:
                # 从描述中提取参数信息
                import re
                param_match = re.search(r"'([^']*\([^)]*\))'", description)
                if param_match:
                    full_signature = param_match.group(1)
                    report.append(f"- **完整签名**: {full_signature}")
            
            report.append(f"- **所在类**: {error.get('class', 'N/A')}")
            report.append(f"- **所在包**: {error.get('package', 'N/A')}")
            report.append("")
            
            report.append("### 需要进一步分析")
            report.append("- [ ] 查找该方法在新框架中的对应实现")
            report.append("- [ ] 分析方法参数类型和返回值")
            report.append("- [ ] 检查是否有替代方法或API")
            report.append("- [ ] 确认导入语句和依赖配置")
            report.append("")
            
            if i < len(errors):
                report.append("---")
                report.append("")
        
        report.append("## 总结")
        report.append("")
        report.append(f"本报告分析了 {len(errors)} 个缺失方法错误。")
        report.append("建议使用知识库搜索相关解决方案，或手动检查源码以确定正确的替代方法。")
        report.append("")
        
        return "\n".join(report)
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def verify_output(self) -> bool:
        """验证输出文件"""
        if not os.path.exists(self.output_file):
            print(f"❌ 输出文件不存在: {self.output_file}")
            return False
        
        try:
            with open(self.output_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                print(f"❌ 输出文件为空: {self.output_file}")
                return False
            
            print(f"✅ 输出文件验证通过: {self.output_file}")
            print(f"   文件大小: {len(content)} 字符")
            return True
            
        except Exception as e:
            print(f"❌ 输出文件验证失败: {e}")
            return False

def main():
    """主函数"""
    print("="*60)
    print("缺失方法源码信息获取处理器")
    print("="*60)
    
    extractor = SourceInfoExtractor()
    
    # 检查输入文件
    if not extractor.check_input_file():
        return False
    
    # 尝试调用source-context-extractor
    success = extractor.call_source_context_extractor()
    
    # 如果失败，生成备用报告
    if not success:
        print("\n⚠️  source-context-extractor调用失败，生成备用报告...")
        success = extractor.generate_fallback_report()
    
    # 验证输出
    if success:
        success = extractor.verify_output()
    
    if success:
        print("\n✅ 缺失方法源码信息获取完成！")
        print(f"报告文件: {extractor.output_file}")
    else:
        print("\n❌ 缺失方法源码信息获取失败！")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
