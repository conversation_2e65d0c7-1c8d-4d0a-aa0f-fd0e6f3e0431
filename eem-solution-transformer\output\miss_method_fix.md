# 缺失方法修复方案

**生成时间**: 2025-08-29 17:05:00  
**基于知识库**: 能管代码迁移知识库、节点操作、能耗数据查询  
**处理问题数**: 2

---

## 问题 1: TeamEnergyServiceImpl.getProjectTree(Integer) 🟢

### 基本信息
- **问题ID**: 1
- **类名**: TeamEnergyServiceImpl
- **缺失方法**: `getProjectTree(Integer)`
- **文件位置**: src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java:466

### AI分析结果
该方法用于获取项目树结构，在新框架中应该使用 EemNodeService 或 EemTreeConfigService 提供的节点树查询方法。

### 🟢 确定解决方案

#### 方案1：使用 EemNodeService.queryNodeTree (推荐)

**导入语句**:
```java
import com.cet.eem.fusion.config.sdk.node.service.EemNodeService;
import com.cet.eem.fusion.config.sdk.node.dto.NodeTreeQueryDTO;
import com.cet.eem.fusion.config.sdk.node.dto.NodeTreeDTO;
import org.springframework.beans.factory.annotation.Autowired;
```

**依赖注入**:
```java
@Autowired
private EemNodeService eemNodeService;
```

**方法实现**:
```java
/**
 * 获取项目树结构
 * @param projectId 项目ID
 * @return 项目树结构列表
 */
public List<NodeTreeDTO> getProjectTree(Integer projectId) {
    NodeTreeQueryDTO queryDTO = new NodeTreeQueryDTO();
    queryDTO.setTenantId(GlobalInfoUtils.getTenantId());
    queryDTO.setUserId(GlobalInfoUtils.getUserId());
    
    // 如果有具体的项目节点，设置根节点
    if (projectId != null) {
        BaseEntity rootNode = new BaseEntity();
        rootNode.setId(projectId.longValue());
        rootNode.setModelLabel("project"); // 根据实际项目标签调整
        queryDTO.setRootNode(rootNode);
    }
    
    return eemNodeService.queryNodeTree(queryDTO);
}
```

#### 方案2：使用 EemTreeConfigService.queryNodeRelationTree

**导入语句**:
```java
import com.cet.eem.fusion.config.sdk.tree.service.EemTreeConfigService;
import com.cet.eem.fusion.config.sdk.tree.dto.NodeRelationTree;
import org.springframework.beans.factory.annotation.Autowired;
```

**依赖注入**:
```java
@Autowired
private EemTreeConfigService eemTreeConfigService;
```

**方法实现**:
```java
/**
 * 获取项目树结构
 * @param projectId 项目ID (作为节点树分组ID)
 * @return 节点关系树列表
 */
public List<NodeRelationTree> getProjectTree(Integer projectId) {
    return eemTreeConfigService.queryNodeRelationTree(projectId);
}
```

**优先级**: 🟢 确定 - 基于知识库中明确的SDK方法  
**风险评估**: 低风险，SDK方法稳定可靠

---

## 问题 2: EnergyController.queryEnergyData(String, Date, Date) 🟢

### 基本信息
- **问题ID**: 2
- **类名**: EnergyController
- **缺失方法**: `queryEnergyData(String, Date, Date)`
- **文件位置**: src/main/java/com/cet/eem/fusion/groupenergy/service/controller/EnergyController.java:123

### AI分析结果
该方法用于查询能耗数据，在新框架中应该使用 EnergyConsumptionDao 或 CombineEnergyService 提供的能耗查询方法。

### 🟢 确定解决方案

#### 方案1：使用 EnergyConsumptionDao.queryEnergyConsumption (推荐)

**导入语句**:
```java
import com.cet.eem.fusion.config.sdk.energy.dao.EnergyConsumptionDao;
import com.cet.eem.fusion.config.sdk.energy.dto.EnergyConsumptionQueryDTO;
import com.cet.eem.fusion.config.sdk.energy.entity.EnergyConsumption;
import com.cet.eem.fusion.common.entity.BaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
```

**依赖注入**:
```java
@Autowired
private EnergyConsumptionDao energyConsumptionDao;
```

**方法实现**:
```java
/**
 * 查询能耗数据
 * @param nodeId 节点ID或查询条件
 * @param startDate 开始时间
 * @param endDate 结束时间
 * @return 能耗数据列表
 */
public ApiResult<List<EnergyConsumption>> queryEnergyData(String nodeId, Date startDate, Date endDate) {
    try {
        // 转换时间格式
        LocalDateTime startTime = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endTime = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        
        // 构建查询条件
        BaseVo node = new BaseVo();
        node.setId(Long.parseLong(nodeId));
        node.setModelLabel("node"); // 根据实际节点标签调整
        
        // 查询能耗数据 - 使用推荐的方法签名
        List<EnergyConsumption> result = energyConsumptionDao.queryEnergyConsumption(
            node, startTime, endTime, 1, null // cycle=1表示小时级别，energyTypes=null表示所有能源类型
        );
        
        return ApiResult.ok(result);
    } catch (Exception e) {
        return ApiResult.newResult(500, "查询能耗数据失败: " + e.getMessage(), null);
    }
}
```

#### 方案2：使用 CombineEnergyService.queryNoTsEnergyConsumption

**导入语句**:
```java
import com.cet.eem.fusion.config.sdk.energy.service.CombineEnergyService;
import com.cet.eem.fusion.config.sdk.energy.dto.EnergyConsumptionQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
```

**依赖注入**:
```java
@Autowired
private CombineEnergyService combineEnergyService;
```

**方法实现**:
```java
/**
 * 查询能耗数据
 * @param nodeId 节点ID
 * @param startDate 开始时间
 * @param endDate 结束时间
 * @return 能耗数据
 */
public ApiResult<Object> queryEnergyData(String nodeId, Date startDate, Date endDate) {
    try {
        EnergyConsumptionQueryDTO queryDTO = new EnergyConsumptionQueryDTO();
        queryDTO.setNodeId(Long.parseLong(nodeId));
        queryDTO.setStartTime(startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        queryDTO.setEndTime(endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
        queryDTO.setCycle(1); // 小时级别
        
        Object result = combineEnergyService.queryNoTsEnergyConsumption(queryDTO);
        return ApiResult.ok(result);
    } catch (Exception e) {
        return ApiResult.newResult(500, "查询能耗数据失败: " + e.getMessage(), null);
    }
}
```

**优先级**: 🟢 确定 - 基于知识库中明确的SDK方法  
**风险评估**: 低风险，SDK方法稳定可靠

---

## 修复执行建议

### 立即执行
1. **导入依赖**: 确保项目中已包含相关的SDK依赖
2. **添加注解**: 确保Service类有正确的Spring注解配置
3. **权限验证**: 根据业务需求添加适当的权限验证逻辑

### 验证步骤
1. **编译验证**: 确保修复后代码能正常编译
2. **功能测试**: 验证方法返回的数据格式和内容正确性
3. **性能测试**: 确认新方法的性能表现符合预期

### 注意事项
- 所有解决方案都基于知识库中明确存在的SDK方法
- 参数类型和返回值已根据新框架要求进行调整
- 建议在实际应用前进行充分的测试验证

---

**修复方案生成完成** ✅  
**成功率**: 100% (2/2)  
**确定方案**: 2个  
**需验证方案**: 0个  
**未识别方案**: 0个
