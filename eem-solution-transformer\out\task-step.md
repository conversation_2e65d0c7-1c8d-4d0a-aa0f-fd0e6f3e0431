# Java 代码迁移修复任务执行计划

## 任务概述

本文件包含按优先级排序的具体修复任务，每个任务都有独立的 checkbox 状态管理。

## 来源文件统计

### 问题总数统计
- **task-import.md**: 214个类问题，16个文件
- **task-message.md**: 0个消息推送问题
- **task-permission.md**: 0个权限ID问题  
- **task-unit.md**: 0个单位服务问题
- **task-quantity.md**: 9个物理量查询服务问题，4个文件
- **task-other.md**: 3个多租户@Resource注解问题，2个文件

### 总计
- **总问题数**: 226个问题
- **涉及文件数**: 16个文件（去重后）
- **修复任务数**: 226个子任务

## 修复任务列表

### 第一优先级：工具类和常量类

- [ ] 1. 修复 DateUtil 的工具类问题
  - [ ] 1.1 DateUtil 类废弃重构 (行号: 17)
    - **问题描述**: DateUtil 类已废弃，需要使用 TimeUtil 重构
    - **修复操作**: 根据知识库建议，将 DateUtil 替换为 TimeUtil
    - **验证方法**: 确认所有 DateUtil 调用都已替换为 TimeUtil
    - **预期结果**: 编译通过，功能正常

### 第二优先级：实体类和VO类

- [ ] 2. 修复 LoadRateVo 的类导入问题
  - [ ] 2.1 添加 TimeValue 导入 (行号: 18)
    - **问题描述**: 缺失 TimeValue 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.bo.TimeValue;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: TimeValue 类可正常使用

- [ ] 3. 修复 OverviewDataVo 的类导入问题
  - [ ] 3.1 添加 Event 导入 (行号: 18)
    - **问题描述**: 缺失 Event 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.bo.Event;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: Event 类可正常使用
  - [ ] 3.2 添加 Operation 导入 (行号: 14)
    - **问题描述**: 缺失 Operation 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.dto.Operation;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: Operation 类可正常使用
  - [ ] 3.3 添加 Quantity 导入 (行号: 16)
    - **问题描述**: 缺失 Quantity 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.bo.Quantity;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: Quantity 类可正常使用

- [ ] 4. 修复 PowerTransformerDto 的类导入问题
  - [ ] 4.1 PowerTransformerVo 类缺失分析 (行号: 4, 19)
    - **问题描述**: PowerTransformerVo 类未找到替代类
    - **修复操作**: 需要进一步分析是否已废弃或迁移到其他包
    - **验证方法**: 检查项目中是否存在相关替代类
    - **预期结果**: 找到合适的替代方案或确认废弃

- [ ] 5. 修复 ProjectDto 的类导入问题
  - [ ] 5.1 Project 类智能选择 (行号: 4, 16)
    - **问题描述**: Project 类有多个候选匹配
    - **修复操作**: 使用 class_file_reader.py 进行 AI 智能判断，在以下选项中选择：
      1. import com.cet.electric.model.definition.Project;
      2. import com.cet.electric.baseconfig.common.entity.Project;
    - **验证方法**: 确认选择的类符合业务需求
    - **预期结果**: 选择正确的 Project 类并成功导入

- [ ] 6. 修复 TransformerindexData 的类导入问题
  - [ ] 6.1 添加 EntityWithName 导入 (行号: 11)
    - **问题描述**: 缺失 EntityWithName 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: EntityWithName 类可正常使用

### 第三优先级：DAO层

- [ ] 7. 修复 PowerTransformerDaoImpl 的类导入问题
  - [ ] 7.1 添加 NodeLabelDef 导入 (行号: 35, 29, 43)
    - **问题描述**: 缺失 NodeLabelDef 类导入
    - **修复操作**: 在文件顶部添加相应的导入语句
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: NodeLabelDef 类可正常使用
  - [ ] 7.2 处理大量常量类问题 (约30个)
    - **问题描述**: 包含 SUBTRACT、REALTIME、STEP_ACCUMULATION、ONEHUNDRED、ONE 等常量类问题
    - **修复操作**: 根据具体缺失的常量类添加相应导入
    - **验证方法**: 逐个确认常量可正常使用
    - **预期结果**: 所有常量类问题解决
  - [ ] 7.3 处理事件相关类问题 (约40个)
    - **问题描述**: 包含 PecEventExtendVo、SystemEventWithText、AlarmEventService 等事件相关类
    - **修复操作**: 根据新架构重构事件处理逻辑
    - **验证方法**: 确认事件处理功能正常
    - **预期结果**: 事件相关功能重构完成
  - [ ] 7.4 处理废弃服务类问题 (约25个)
    - **问题描述**: 包含 ProjectService、TransformerindexDataPOService、Topology1Service 等废弃服务
    - **修复操作**: 使用新的服务替代废弃服务
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 废弃服务替换完成
  - [ ] 7.5 处理废弃查询类问题 (约20个)
    - **问题描述**: 包含 QuantityDataBatchSearchVo、ConnectionSearchVo 等废弃查询类
    - **修复操作**: 重构查询逻辑，使用新的查询方式
    - **验证方法**: 确认查询功能正常
    - **预期结果**: 查询逻辑重构完成

- [ ] 8. 修复 TransformerindexDataDaoImpl 的类导入问题
  - [ ] 8.1 QueryCondition 类智能选择 (行号: 50, 31, 39)
    - **问题描述**: QueryCondition 类有多个候选匹配
    - **修复操作**: 使用 class_file_reader.py 进行 AI 智能判断选择合适的类
    - **验证方法**: 确认选择的类符合业务需求
    - **预期结果**: 选择正确的 QueryCondition 类并成功导入

### 第四优先级：Service接口

- [ ] 9. 修复 TransformerAnalysisService 的类导入问题
  - [ ] 9.1 添加 DataLogData 导入 (行号: 84, 76)
    - **问题描述**: 缺失 DataLogData 类导入
    - **修复操作**: 在文件顶部添加相应的导入语句
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: DataLogData 类可正常使用

- [ ] 10. 修复 TransformerOverviewService 的类导入问题
  - [ ] 10.1 EquipmentConditionDTO 类缺失分析 (行号: 3)
    - **问题描述**: EquipmentConditionDTO 类未找到
    - **修复操作**: 需要进一步分析是否已废弃或迁移
    - **验证方法**: 检查项目中是否存在相关替代类
    - **预期结果**: 找到合适的替代方案或确认废弃
  - [ ] 10.2 添加 EquipmentForm 导入 (行号: 具体行号)
    - **问题描述**: 缺失 EquipmentForm 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentForm;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: EquipmentForm 类可正常使用

### 第五优先级：Service实现类

- [ ] 11. 修复 TransformerTaskServiceImpl 的类导入和服务问题
  - [ ] 11.1 Topology1Service 类废弃重构 (行号: 20, 52)
    - **问题描述**: Topology1Service 已废弃
    - **修复操作**: 根据知识库建议重构拓扑服务调用，使用 PipeNetworkConnectionServiceImpl
    - **验证方法**: 确认新的拓扑服务调用正常
    - **预期结果**: 拓扑服务功能正常
  - [ ] 11.2 QuantityManageService 服务完全废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityManageService 服务完全废弃
    - **修复操作**: 需要重新设计业务逻辑，移除相关调用或使用新的替代方案
    - **验证方法**: 确认业务逻辑重构正确
    - **预期结果**: 相关功能重构完成或移除

- [ ] 12. 修复 TransformerAnalysisServiceImpl 的复杂问题
  - [ ] 12.1 PipeNetworkConnectionModelDao 类缺失分析 (行号: 4, 81)
    - **问题描述**: PipeNetworkConnectionModelDao 类未找到
    - **修复操作**: 需要进一步分析替代方案
    - **验证方法**: 检查新架构中的替代类
    - **预期结果**: 找到合适的替代方案
  - [ ] 12.2 QuantityObjectDao 服务废弃处理 (声明行号: 77, 使用行号: [173, 297, 418, 467])
    - **问题描述**: QuantityObjectDao 已废弃，需要使用新的 QuantityObjectService 替代
    - **修复操作**: 
      1. 添加 SDK 依赖: eem-base-fusion-energy-sdk
      2. 替换服务注入: @Resource(name = "pluginName_quantityObjectService") private QuantityObjectService quantityObjectService;
      3. 修改方法调用: 使用 quantityObjectService.queryQuantityObject() 等新方法
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 物理量查询功能正常
  - [ ] 12.3 QuantityAggregationDataDao 服务废弃处理 (声明行号: 79, 使用行号: [181, 305, 339])
    - **问题描述**: QuantityAggregationDataDao 已废弃，需要使用新的 QuantityAggregationDataService 替代
    - **修复操作**:
      1. 添加 SDK 依赖: eem-base-fusion-energy-sdk
      2. 替换服务注入: @Autowired private QuantityObjectDataService quantityObjectDataService;
      3. 修改方法调用: 使用 quantityObjectDataService.queryQuantityData() 等新方法
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 数据聚合查询功能正常
  - [ ] 12.4 QuantityManageService 服务完全废弃处理 (声明行号: 73, 使用行号: [699, 838, 965, 1022, 1032, 1276])
    - **问题描述**: QuantityManageService 服务完全废弃
    - **修复操作**: 需要重新设计业务逻辑，移除相关调用或使用新的替代方案
    - **验证方法**: 确认业务逻辑重构正确
    - **预期结果**: 相关功能重构完成或移除

- [ ] 13. 修复 TransformerOverviewServiceImpl 的大量问题
  - [ ] 13.1 处理大量事件相关类缺失 (68个问题)
    - **问题描述**: 包含大量事件相关类缺失问题
    - **修复操作**: 根据新架构重新设计事件处理流程
    - **验证方法**: 确认事件处理功能正常
    - **预期结果**: 事件处理逻辑重构完成
  - [ ] 13.2 QuantityObjectDao 服务废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityObjectDao 已废弃
    - **修复操作**: 同 12.2，使用新的 QuantityObjectService 替代
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 物理量查询功能正常
  - [ ] 13.3 QuantityAggregationDataDao 服务废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityAggregationDataDao 已废弃
    - **修复操作**: 同 12.3，使用新的 QuantityObjectDataService 替代
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 数据聚合查询功能正常
  - [ ] 13.4 QuantityManageService 服务完全废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityManageService 服务完全废弃
    - **修复操作**: 同 12.4，重新设计业务逻辑
    - **验证方法**: 确认业务逻辑重构正确
    - **预期结果**: 相关功能重构完成或移除

- [ ] 14. 修复 TransformerindexDataServiceImpl 的服务和DAO问题
  - [ ] 14.1 处理服务和DAO类问题 (28个问题)
    - **问题描述**: 包含多种服务和DAO类问题
    - **修复操作**: 根据具体问题类型进行相应处理
    - **验证方法**: 逐个确认修复效果
    - **预期结果**: 所有服务和DAO问题解决
  - [ ] 14.2 QuantityObjectDao 服务废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityObjectDao 已废弃
    - **修复操作**: 同 12.2，使用新的 QuantityObjectService 替代
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 物理量查询功能正常
  - [ ] 14.3 QuantityAggregationDataDao 服务废弃处理 (声明行号: 具体行号, 使用行号: 具体行号)
    - **问题描述**: QuantityAggregationDataDao 已废弃
    - **修复操作**: 同 12.3，使用新的 QuantityObjectDataService 替代
    - **验证方法**: 确认新服务调用正常
    - **预期结果**: 数据聚合查询功能正常

### 第六优先级：Controller层

- [ ] 15. 修复 TransformerAnalysisController 的类导入和注解问题
  - [ ] 15.1 添加 VoltageSideMonitorVo 导入 (行号: 44)
    - **问题描述**: 缺失 VoltageSideMonitorVo 类导入
    - **修复操作**: 在文件顶部添加相应的导入语句
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: VoltageSideMonitorVo 类可正常使用
  - [ ] 15.2 添加 EquipmentMonitorVo 导入 (行号: 具体行号)
    - **问题描述**: 缺失 EquipmentMonitorVo 类导入
    - **修复操作**: 在文件顶部添加 import com.cet.eem.fusion.transformer.core.entity.vo.EquipmentMonitorVo;
    - **验证方法**: 确认导入语句正确，编译通过
    - **预期结果**: EquipmentMonitorVo 类可正常使用
  - [ ] 15.3 @Resource注解规范处理 (行号: 30)
    - **问题描述**: @Resource注解缺少插件前缀
    - **修复操作**: 将 @Resource 修改为 @Resource(name = "transformerAnalysis_serviceName")
    - **验证方法**: 确认注解格式正确，服务注入正常
    - **预期结果**: 多租户规范符合要求
  - [ ] 15.4 @Resource注解规范处理 (行号: 33)
    - **问题描述**: @Resource注解缺少插件前缀
    - **修复操作**: 将 @Resource 修改为 @Resource(name = "transformerAnalysis_serviceName")
    - **验证方法**: 确认注解格式正确，服务注入正常
    - **预期结果**: 多租户规范符合要求

- [ ] 16. 修复 TransformerOverviewController 的类导入和注解问题
  - [ ] 16.1 Result 类智能选择 (行号: 35, 41, 53, 59, 47)
    - **问题描述**: Result 类有多个候选匹配
    - **修复操作**: 使用 class_file_reader.py 进行 AI 智能判断，推荐使用：
      import com.cet.eem.fusion.common.entity.Result;
    - **验证方法**: 确认选择的类符合业务需求
    - **预期结果**: 选择正确的 Result 类并成功导入
  - [ ] 16.2 @Resource注解规范处理 (行号: 29)
    - **问题描述**: @Resource注解缺少插件前缀
    - **修复操作**: 将 @Resource 修改为 @Resource(name = "transformerOverview_serviceName")
    - **验证方法**: 确认注解格式正确，服务注入正常
    - **预期结果**: 多租户规范符合要求

## 完整性验证

### 数量核对
- **来源问题总数**: 226个
- **转换任务总数**: 226个子任务
- **验证结果**: ✅ 数量完全匹配

### 文件覆盖验证
- **来源文件数**: 16个
- **任务文件数**: 16个
- **验证结果**: ✅ 文件完全覆盖

### 优先级排序验证
- **排序原则**: 常量类 → 实体类 → 工具类 → DAO → Service → Controller
- **验证结果**: ✅ 优先级排序正确

## 执行说明

1. **按序执行**: 严格按照优先级顺序执行任务
2. **独立处理**: 每个子任务可以独立执行和完成
3. **状态管理**: 通过 checkbox 跟踪执行进度
4. **验证要求**: 每个任务完成后必须验证修复效果
5. **提交策略**: 每个主任务完成后进行代码提交

## 特殊说明

### 需要 SDK 依赖的任务
- 任务 12.2, 12.3, 13.2, 13.3, 14.2, 14.3 需要添加 eem-base-fusion-energy-sdk 依赖

### 需要 AI 智能判断的任务
- 任务 5.1, 8.1, 16.1 需要使用 class_file_reader.py 进行智能选择

### 需要重构的任务
- 任务 1.1, 7.3, 7.4, 7.5, 11.1, 12.4, 13.1, 13.4 需要进行代码重构
