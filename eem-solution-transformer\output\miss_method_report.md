# 源码上下文分析报告
**生成时间**: 2025-08-29 17:00:30

## 问题 1: getProjectTree(Integer)

```json
{
  "missing_method": "getProjectTree(Integer)",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'getProjectTree(Integer)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 1,
    "error_code": "miss_method",
    "module": "eem-solution-transformer-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getProjectTree(Integer)",
    "description": "无法解析方法 'getProjectTree(Integer)'",
    "line": "466"
  }
}
```

## 问题 2: queryEnergyData(String, Date, Date)

```json
{
  "missing_method": "queryEnergyData(String, Date, Date)",
  "in_param": {},
  "out_return": "Object",
  "context": "无法解析方法 'queryEnergyData(String, Date, Date)'",
  "content": "",
  "notes": "未找到原始方法实现",
  "original_issue": {
    "issue_id": 2,
    "error_code": "miss_method",
    "module": "eem-solution-transformer-service",
    "package": "com.cet.eem.fusion.groupenergy.service.controller",
    "class": "EnergyController",
    "missing_method": "queryEnergyData(String, Date, Date)",
    "description": "无法解析方法 'queryEnergyData(String, Date, Date)'",
    "line": "123"
  }
}
```
