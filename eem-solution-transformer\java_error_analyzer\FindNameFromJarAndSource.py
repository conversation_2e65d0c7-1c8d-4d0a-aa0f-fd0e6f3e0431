import os
import subprocess
import zipfile
import sys
from pathlib import Path
import glob

def find_java_files_in_project(project_path, class_name):
    """在项目源码中搜索Java类文件"""
    results = []
    project_path = Path(project_path)
    
    # 常见的源码目录
    source_dirs = [
        "src/main/java",
        "src/test/java",
        "*/src/main/java",  # 多模块项目
        "*/src/test/java"   # 多模块项目
    ]
    
    for source_pattern in source_dirs:
        source_paths = list(project_path.glob(source_pattern))
        
        for source_path in source_paths:
            if source_path.is_dir():
                # 搜索匹配的Java文件
                java_files = list(source_path.rglob(f"{class_name}.java"))
                
                for java_file in java_files:
                    # 计算包名
                    relative_path = java_file.relative_to(source_path)
                    package_path = relative_path.parent
                    package_name = str(package_path).replace(os.sep, ".") if package_path != Path(".") else ""
                    
                    full_qualified_name = f"{package_name}.{class_name}" if package_name else class_name
                    
                    results.append({
                        'class_name': class_name,
                        'full_qualified_name': full_qualified_name,
                        'file_path': str(java_file),
                        'source_type': 'project_source'
                    })
                    
                    print(f"找到项目源码类 {class_name} 于: {java_file}")
                    print(f"  全限定名: {full_qualified_name}")
    
    return results

def run_maven_build_classpath(project_path, modules=None):
    """在多模块项目中构建classpath"""
    # 解析为绝对路径
    project_path = os.path.abspath(project_path)
    
    # 强制从配置文件获取模块列表
    if modules is None:
        try:
            from config_utils import ConfigManager
            config = ConfigManager()
            modules = config.get_modules()
            if not modules:
                raise ValueError("配置文件中未设置扫描模块。请在 config.yaml 中设置 project.modules。")
        except ImportError:
            raise RuntimeError("无法导入配置管理器。请确保 config_utils.py 文件存在。")
        except Exception as e:
            raise RuntimeError(f"无法加载配置文件: {e}. 请确保 config.yaml 文件存在并配置正确。")
    
    all_jars = set()

    for module in modules:
        module_path = os.path.join(project_path, module)
        if not os.path.exists(module_path):
            print(f"模块路径不存在: {module_path}")
            continue
            
        # 输出文件路径相对于模块目录
        classpath_file = "classpath.txt"
        
        # 自动检测mvn或mvn.cmd
        mvn_executable = "mvn.cmd" if sys.platform == "win32" else "mvn"
        cmd = [
            mvn_executable,
            "dependency:build-classpath",
            "-DincludeScope=compile",
            f"-Dmdep.outputFile={classpath_file}",
            "-Dmaven.main.skip=true",
            "-Dmaven.test.skip=true"
        ]
        
        print(f"在模块 {module} 中执行命令:", " ".join(cmd))
        proc = subprocess.run(cmd, cwd=module_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if proc.returncode != 0:
            print(f"模块 {module} 的Maven执行失败:")
            print(proc.stderr)
            continue
        
        # 构造classpath.txt的完整路径用于读取
        full_classpath_file = os.path.join(module_path, classpath_file)
        if not os.path.exists(full_classpath_file):
            print(f"模块 {module} 的classpath.txt文件未生成")
            continue
            
        with open(full_classpath_file, "r", encoding="utf-8") as f:
            jars = f.read().strip().split(os.pathsep)
            # 过滤掉空字符串
            all_jars.update(jar for jar in jars if jar)

    if not all_jars:
        print("所有模块都未能成功生成classpath。")
        return None

    print(f"共找到 {len(all_jars)} 个唯一的依赖jar")
    return list(all_jars)

def search_class_in_jars(class_name, jar_paths):
    """在JAR文件中搜索指定的类"""
    results = []
    suffix = f"/{class_name}.class"
    
    for jar in jar_paths:
        try:
            with zipfile.ZipFile(jar, "r") as zipf:
                for entry in zipf.namelist():
                    if entry.endswith(suffix):
                        full_class_name = entry.replace("/", ".").replace(".class", "")
                        print(f"找到JAR包类 {class_name} 于: {jar}")
                        print(f"  全限定名: {full_class_name}")
                        results.append({
                            'class_name': class_name,
                            'full_qualified_name': full_class_name,
                            'jar_path': jar,
                            'source_type': 'jar_dependency'
                        })
        except Exception as e:
            # 忽略坏的zip文件，但记录错误
            print(f"警告: 无法读取JAR文件 {jar}: {e}")
            continue
    
    return results

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python FindNameFromJarAndSource.py <类名> [项目路径]")
        print("如果不指定项目路径，将从配置文件中读取")
        sys.exit(1)
    
    class_name = sys.argv[1]
    
    # 强制从配置文件加载配置
    try:
        from config_utils import ConfigManager
        config = ConfigManager()
        
        # 如果提供了项目路径参数，则使用它，否则使用配置文件中的路径
        if len(sys.argv) > 2:
            project_path = sys.argv[2]
        else:
            project_path = config.get_project_root()
            if not project_path:
                raise ValueError("项目路径未配置。请在 config.yaml 中设置 project.root_path 或通过参数传入。")
        
        modules = config.get_modules()
        if not modules:
            raise ValueError("扫描模块未配置。请在 config.yaml 中设置 project.modules。")
        
    except ImportError:
        print("❌ 错误: 无法导入配置管理器")
        print("请确保 config_utils.py 文件存在")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("请检查 config.yaml 配置文件是否存在并配置正确")
        sys.exit(1)
    
    # 确保项目路径是绝对路径
    project_path = os.path.abspath(project_path)
    
    print(f"搜索类: {class_name}")
    print(f"项目路径: {project_path}")
    print(f"扫描模块: {', '.join(modules)}")
    print("-" * 50)
    
    all_results = []
    
    # 1. 首先搜索项目源码
    print("🔍 搜索项目源码...")
    source_results = find_java_files_in_project(project_path, class_name)
    all_results.extend(source_results)
    
    # 2. 然后搜索JAR包依赖
    print("\n🔍 搜索JAR包依赖...")
    jar_paths = run_maven_build_classpath(project_path, modules)
    
    if jar_paths:
        jar_results = search_class_in_jars(class_name, jar_paths)
        all_results.extend(jar_results)
    
    # 3. 输出结果
    if all_results:
        print(f"\n✅ 搜索完成，找到 {len(all_results)} 个匹配项")
        print("=" * 50)
        
        # 按来源分组显示
        source_results = [r for r in all_results if r.get('source_type') == 'project_source']
        jar_results = [r for r in all_results if r.get('source_type') == 'jar_dependency']
        
        if source_results:
            print(f"\n📁 项目源码中找到 {len(source_results)} 个:")
            for result in source_results:
                print(f"  类名: {result['class_name']}")
                print(f"  全限定名: {result['full_qualified_name']}")
                print(f"  文件路径: {result['file_path']}")
                print("-" * 30)
        
        if jar_results:
            print(f"\n📦 JAR包依赖中找到 {len(jar_results)} 个:")
            for result in jar_results:
                print(f"  类名: {result['class_name']}")
                print(f"  全限定名: {result['full_qualified_name']}")
                print(f"  JAR路径: {result['jar_path']}")
                print("-" * 30)
    else:
        print(f"\n❌ 搜索完成，未找到类 {class_name}")
        print("💡 建议检查:")
        print("  1. 类名拼写是否正确")
        print("  2. 项目是否已编译")
        print("  3. Maven依赖是否正确配置")

if __name__ == "__main__":
    main()