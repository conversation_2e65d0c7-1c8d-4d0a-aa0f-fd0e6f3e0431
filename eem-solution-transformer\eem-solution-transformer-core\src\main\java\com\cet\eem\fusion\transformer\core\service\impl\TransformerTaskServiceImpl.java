package com.cet.eem.fusion.transformer.core.service.impl;

import cn.hutool.core.date.StopWatch;
import com.cet.eem.fusion.common.def.quantity.FrequencyDef;
import com.cet.eem.fusion.common.def.quantity.PhasorDef;
import com.cet.eem.fusion.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.fusion.common.def.quantity.QuantityTypeDef;
import com.cet.electric.baseconfig.common.entity.Project;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.def.base.EnergyTypeDef;
import com.cet.eem.fusion.common.def.base.EnumDataTypeId;
import com.cet.eem.fusion.common.def.common.EnumOperationType;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.model.datalog.DataLogData;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.fusion.common.model.quantity.QuantitySearchVo;
import com.cet.eem.fusion.transformer.core.entity.bo.HistoricalLoadBO;
import com.cet.eem.fusion.transformer.core.service.TransformerTaskService;
import org.apache.commons.collections4.CollectionUtils;
import com.cet.eem.fusion.transformer.core.dao.PowerTransformerDao;
import com.cet.eem.fusion.transformer.core.entity.dto.PowerTransformerDTO;
import com.cet.eem.fusion.transformer.core.service.TransformerAnalysisService;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class TransformerTaskServiceImpl implements TransformerTaskService {

    protected static final Logger logger = LoggerFactory.getLogger(TransformerTaskServiceImpl.class);

    private static final String LOG_KEY = "[变压器历史最大负载计算]";

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    TransformerAnalysisService transformerAnalysisService;
    @Autowired
    PowerTransformerDao powerTransformerDao;


    @Override
    public void historicalLoadCalculation(Long time) throws InstantiationException, IllegalAccessException {
        logger.info("{}开始执行", LOG_KEY);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        int num = 0;
        List<HistoricalLoadVo> historicalLoad = getHistoricalLoad();
        //获取所有项目 逐个项目查询变压器 处理数据 入库
        List<Long> projectIds = getProjectID();
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }
        for (int i = 0; i < projectIds.size(); i++) {
            Long projectId = projectIds.get(i);
            List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);
            List<HistoricalLoadVo> resData = calculateHistoricalData(getTodayZero(time), transformers, historicalLoad, projectId);
            num = num + resData.size();
            modelServiceUtils.writeData(resData, HistoricalLoadVo.class);
        }
        stopWatch.stop();
        logger.info("{}执行完成: {} ", LOG_KEY, TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
        logger.info("{}总共用时：{} ms，共更新{}条数据", LOG_KEY, stopWatch.getTotalTimeMillis(), num);
    }

    private List<Long> getProjectID() {
        QueryCondition queryCondition = ParentQueryConditionBuilder.of(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, Project.class).stream().map(Project::getId).collect(Collectors.toList());
    }


    /**
     * 查询变压器历史负载记录
     */
    private List<HistoricalLoadVo> getHistoricalLoad() {
        QueryCondition queryCondition = ParentQueryConditionBuilder.of("historicalload").build();
        return modelServiceUtils.query(queryCondition, HistoricalLoadVo.class);
    }

    /**
     * 根据有功、无功电度计算负载信息
     */
    private List<HistoricalLoadVo> calculateHistoricalData(Long time, List<PowerTransformerDto> transformerVos, List<HistoricalLoadVo> historicalLoad, Long projectId) throws InstantiationException, IllegalAccessException {
        List<HistoricalLoadVo> resData = new ArrayList<>();
        QuantityDataBatchSearchVo quantitySearchVo = createQuantitySearchVo(time);
        logger.info("{}本次计算的起点时间是：{}", LOG_KEY, TimeUtil.timestamp2LocalDateTime(quantitySearchVo.getStartTime()));
        List<List<PowerTransformerDto>> tmp = fixedGrouping(transformerVos, 50);
        logger.info("{}查询项目id{}下的所有变压器信息，总数：{}", LOG_KEY, projectId, transformerVos.size());
        //分隔成50个为一组进行调用
        tmp.forEach(list -> {
            Map<Long, List<DataLogData>> longListMap = transformerAnalysisService.queryLoadRateBatch(list, quantitySearchVo, projectId);
            for (Map.Entry<Long, List<DataLogData>> entry : longListMap.entrySet()) {
                Long transformerID = entry.getKey();
                HistoricalLoadVo historicalLoadVo = historicalLoad.stream().filter(x -> Objects.equals(x.getPowertransformerid(), transformerID)).findFirst().orElse(null);
                if (Objects.nonNull(historicalLoadVo) && time < historicalLoadVo.getLogtime()) {
                    continue;
                }
                DataLogData value = entry.getValue().stream().filter(x -> Objects.nonNull(x.getValue())).max(Comparator.comparingDouble(DataLogData::getValue)).orElse(new DataLogData(time, 0.0));
                PowerTransformerDto transformerDto = list.stream().filter(x -> Objects.equals(x.getId(), transformerID)).findFirst().orElse(null);
                if (Objects.isNull(transformerDto)) {
                    continue;
                }
                HistoricalLoadVo res = compareHistoricalLoad(time, historicalLoadVo, value, transformerDto);
                if (Objects.nonNull(res)) {
                    resData.add(res);
                }
            }
        });
        return resData;
    }

    /**
     * 构造定时记录查询条件
     */
    private QuantityDataBatchSearchVo createQuantitySearchVo(Long time) {
        QuantityDataBatchSearchVo quantityDataBatchSearchVo = new QuantityDataBatchSearchVo();
        //当historicalload中无记录时，查询变压器历史所有定时记录；有记录时，查询上次录入时间到现在的定时记录
        quantityDataBatchSearchVo.setStartTime(TimeUtil.addDateTimeByCycle(time, AggregationCycle.ONE_MONTH, -1));
        quantityDataBatchSearchVo.setEndTime(time);
        quantityDataBatchSearchVo.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        quantityDataBatchSearchVo.setAggregationCycle(AggregationCycle.ONE_HOUR);
        quantityDataBatchSearchVo.setQuantitySettings(Arrays.asList(getPositiveActiveElectric(), getReactivePowerElectric()));
        quantityDataBatchSearchVo.setNodes(null);
        return quantityDataBatchSearchVo;
    }

    /**
     * 比较历史负载最值，当新时段的平均负载值/率 大于 历史平均负载值/率时，更新；反之不动
     */
    private HistoricalLoadVo compareHistoricalLoad(Long time, HistoricalLoadVo oldHistoricalLoadVo, DataLogData dataLogData, PowerTransformerDto item) {
        if (Objects.isNull(oldHistoricalLoadVo)) {
            if (Objects.isNull(dataLogData)) {
                return null;
            }
            HistoricalLoadVo res = new HistoricalLoadVo();
            res.setId(0);
            res.setPowertransformerid(item.getId());
            res.setHismaxload(dataLogData.getValue());
            res.setHismaxloadrate(NumberCalcUtils.calcDouble(dataLogData.getValue(), item.getRatedcapacity(), EnumOperationType.DIVISION.getId()));
            res.setAggregationcycle(AggregationCycle.ONE_HOUR);
            res.setOccurrencetime(dataLogData.getTime());
            res.setLogtime(time);
            return res;
        } else {
            if (Objects.isNull(dataLogData)) {
                oldHistoricalLoadVo.setLogtime(time);
                return oldHistoricalLoadVo;
            }
            if (dataLogData.getValue() >= oldHistoricalLoadVo.getHismaxload()) {
                HistoricalLoadVo res = new HistoricalLoadVo();
                res.setId(oldHistoricalLoadVo.getId());
                res.setPowertransformerid(item.getId());
                res.setHismaxload(dataLogData.getValue());
                res.setHismaxloadrate(NumberCalcUtils.calcDouble(dataLogData.getValue(), item.getRatedcapacity(), EnumOperationType.DIVISION.getId()));
                res.setAggregationcycle(AggregationCycle.ONE_HOUR);
                res.setOccurrencetime(dataLogData.getTime());
                res.setLogtime(time);
                return res;
            }
            oldHistoricalLoadVo.setLogtime(time);
            return oldHistoricalLoadVo;
        }
    }

    private QuantitySearchVo getPositiveActiveElectric() {
        return new QuantitySearchVo(4000004,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.P_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getReactivePowerElectric() {
        return new QuantitySearchVo(4000020,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.Q_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    private List<T> getList(String s, Map<String, Object> nodeRelation) {
        return (List) nodeRelation.get(s);
    }


    /**
     * 将一组数据固定分组，每组n个元素
     *
     * @param source 要分组的数据源
     * @param n      每组n个元素
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> fixedGrouping(List<T> source, int n) {

        if (null == source || source.size() == 0 || n <= 0) {
            return null;
        }
        List<List<T>> result = new ArrayList<List<T>>();

        int sourceSize = source.size();
        int size = (source.size() / n) + 1;
        for (int i = 0; i < size; i++) {
            List<T> subset = new ArrayList<T>();
            for (int j = i * n; j < (i + 1) * n; j++) {
                if (j < sourceSize) {
                    subset.add(source.get(j));
                }
            }
            result.add(subset);
        }
        return result;
    }

    private static Long getTodayZero(Long time) {
        Long a = (long) (60 * 60 * 24 * 1000);
        return time - (time + 60 * 60 * 8 * 1000) % a;
    }
}
