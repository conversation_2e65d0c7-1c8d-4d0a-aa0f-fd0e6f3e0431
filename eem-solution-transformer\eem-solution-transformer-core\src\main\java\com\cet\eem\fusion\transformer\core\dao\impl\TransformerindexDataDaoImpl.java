package com.cet.eem.fusion.transformer.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;
import com.cet.eem.fusion.transformer.core.dao.TransformerindexDataDao;
import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexData;
import com.cet.eem.fusion.transformer.core.def.TransformerConstantDef;
import com.cet.eem.transformer.model.constant.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : TransformerindexDataDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-28 10:33
 */
@Component
public class TransformerindexDataDaoImpl implements TransformerindexDataDao {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<TransformerindexData> save(List<TransformerindexData> avgloadandpowers) {
        return modelServiceUtils.writeData(avgloadandpowers, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .in(Constant.LOGTIME, times)
                .build();
        return modelServiceUtils.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByParams(Long logTime, Integer cycle,Integer type, Boolean desc) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .eq(Constant.LOGTIME, logTime)
                .eq(Constant.AGGREGATIONCYCLE,cycle)
                .eq(Constant.TYPE,type)
                .orderBy(Constant.VALUE,desc)
                .build();
        return modelServiceUtils.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times, Integer type, Integer cycle) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .in(Constant.LOGTIME, times)
                .eq(Constant.COLUMN_AGGREGATION_CYCLE, cycle)
                .eq(Constant.TYPE, type)
                .build();
        return modelServiceUtils.query(condition, TransformerindexData.class);
    }

    @Override
    public List<EemPoiRecord> saveEemPoiRecord(List<EemPoiRecord> pois) {
        return modelServiceUtils.writeData(pois,EemPoiRecord.class);
    }
}
