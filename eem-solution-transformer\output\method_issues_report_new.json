{"title": "方法扫描问题报告", "generation_time": "2025-08-29 16:50:00", "total_count": 2, "scan_summary": {"project_path": "E:\\work\\project\\ai-solution-eem-service\\energy-solution-fusion\\eem-solution-transformer", "modules_scanned": ["eem-solution-transformer-core", "eem-solution-transformer-service"], "inspection_profile": "ai_method.xml", "scan_status": "completed"}, "issues_by_class": {"TeamEnergyServiceImpl": [{"issue_id": 1, "error_code": "miss_method", "module": "eem-solution-transformer-core", "package": "com.cet.eem.fusion.groupenergy.core.service.impl", "class": "TeamEnergyServiceImpl", "missing_method": "getProjectTree(Integer)", "description": "无法解析方法 'getProjectTree(Integer)'", "line": "466", "file_path": "src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java", "severity": "ERROR", "category": "method_resolution"}], "EnergyController": [{"issue_id": 2, "error_code": "miss_method", "module": "eem-solution-transformer-service", "package": "com.cet.eem.fusion.groupenergy.service.controller", "class": "EnergyController", "missing_method": "queryEnergyData(String, Date, Date)", "description": "无法解析方法 'queryEnergyData(String, Date, Date)'", "line": "123", "file_path": "src/main/java/com/cet/eem/fusion/groupenergy/service/controller/EnergyController.java", "severity": "ERROR", "category": "method_resolution"}]}, "statistics": {"by_error_code": {"miss_method": 2, "wrong_params": 0, "unidentified": 0}, "by_module": {"eem-solution-transformer-core": 1, "eem-solution-transformer-service": 1}, "by_severity": {"ERROR": 2, "WARNING": 0, "INFO": 0}}, "next_steps": ["运行源码上下文提取器分析缺失方法", "在知识库中搜索替代方案", "生成具体的修复建议", "执行代码修复"]}