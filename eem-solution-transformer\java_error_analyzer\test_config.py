#!/usr/bin/env python3
"""
测试配置文件加载
"""
import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config_utils import ConfigManager

def main():
    """测试配置加载"""
    print("=== 测试 Java Error Analyzer 配置 ===\n")
    
    try:
        # 创建配置管理器
        config = ConfigManager()
        
        # 打印配置摘要
        config.print_config_summary()
        
        # 测试各种配置获取方法
        print("\n=== 详细配置信息 ===")
        print(f"项目根目录: {config.get_project_root()}")
        print(f"扫描模块: {config.get_modules()}")
        print(f"源码模式: {config.get_source_patterns()}")
        print(f"排除模式: {config.get_exclude_patterns()}")
        
        # 测试嵌套配置获取
        print(f"\n检查配置文件路径: {config.get_nested('inspection.profile_path')}")
        print(f"日志级别: {config.get_nested('logging.level')}")
        print(f"超时时间: {config.get_nested('processing.timeout_seconds')}")
        
        # 测试处理配置对象
        processing_config = config.get_processing_config()
        print(f"\n处理配置对象:")
        print(f"  脚本路径: {processing_config.script_path}")
        print(f"  项目路径: {processing_config.project_path}")
        print(f"  超时时间: {processing_config.timeout_seconds}")
        print(f"  最大结果: {processing_config.max_results}")
        
        # 测试错误分类模式
        patterns = config.get_error_classification_patterns()
        if patterns:
            print(f"\n错误分类模式:")
            for category, pattern_config in patterns.items():
                keywords = pattern_config.get("keywords", [])
                priority = pattern_config.get("priority", "未设置")
                print(f"  {category}: {len(keywords)} 个关键词, 优先级: {priority}")
        
        print("\n✅ 配置加载测试成功！")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()