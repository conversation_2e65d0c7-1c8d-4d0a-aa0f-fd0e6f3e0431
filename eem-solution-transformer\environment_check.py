#!/usr/bin/env python3
"""
环境准备和初始化验证脚本
验证智能错误解析器所需的所有组件和文件
"""

import os
import sys
import json
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_directory_exists(dir_path, description):
    """检查目录是否存在"""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} (不存在)")
        return False

def check_json_file_format(file_path, description):
    """检查JSON文件格式是否正确"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ {description}: 格式正确，包含 {len(data)} 个错误记录")
        return True, data
    except Exception as e:
        print(f"❌ {description}: JSON格式错误 - {e}")
        return False, None

def check_knowledge_base():
    """检查知识库完整性"""
    kb_path = "知识库"
    if not os.path.exists(kb_path):
        print(f"❌ 知识库目录不存在: {kb_path}")
        return False
    
    required_files = [
        "03 节点操作.md",
        "04 权限相关.md", 
        "06 能耗数据查询.md",
        "07 系统事件查询.md",
        "08 设备数据服务封装.md",
        "能管代码迁移知识库.md"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = os.path.join(kb_path, file_name)
        if os.path.exists(file_path):
            print(f"✅ 知识库文件: {file_name}")
        else:
            print(f"❌ 知识库文件缺失: {file_name}")
            all_exist = False
    
    return all_exist

def check_source_context_extractor():
    """检查source-context-extractor工具"""
    tool_path = "source_context_extractor"
    if not os.path.exists(tool_path):
        print(f"❌ source-context-extractor目录不存在: {tool_path}")
        return False
    
    required_files = [
        "main.py",
        "config.yaml", 
        "requirements.txt",
        "models.py",
        "context_extractor.py"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = os.path.join(tool_path, file_name)
        if os.path.exists(file_path):
            print(f"✅ source-context-extractor文件: {file_name}")
        else:
            print(f"❌ source-context-extractor文件缺失: {file_name}")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("=" * 60)
    print("智能错误解析器 - 环境准备和初始化检查")
    print("=" * 60)
    
    # 检查输出目录
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"✅ 创建输出目录: {output_dir}")
    else:
        print(f"✅ 输出目录已存在: {output_dir}")
    
    # 检查错误数据源文件
    method_issues_file = "output/method_issues_report.json"
    file_exists = check_file_exists(method_issues_file, "错误数据源文件")
    
    if file_exists:
        is_valid, data = check_json_file_format(method_issues_file, "错误数据源文件格式")
        if is_valid and data:
            print(f"   - 包含错误记录数量: {len(data)}")
            if len(data) > 0:
                print(f"   - 示例错误ID: {data[0].get('issue_id', 'N/A')}")
    
    # 检查source-context-extractor工具
    print("\n" + "-" * 40)
    print("检查 source-context-extractor 工具")
    print("-" * 40)
    extractor_ok = check_source_context_extractor()
    
    # 检查配置文件
    config_file = "source_context_extractor/config.yaml"
    check_file_exists(config_file, "source-context-extractor配置文件")
    
    # 检查知识库
    print("\n" + "-" * 40)
    print("检查知识库完整性")
    print("-" * 40)
    kb_ok = check_knowledge_base()
    
    # 检查Java错误分析器
    print("\n" + "-" * 40)
    print("检查Java错误分析器")
    print("-" * 40)
    analyzer_files = [
        "java_error_analyzer/main.py",
        "java_error_analyzer/FindNameFromJarAndSource.py",
        "java_error_analyzer/error_parser.py",
        "java_error_analyzer/models.py"
    ]
    
    analyzer_ok = True
    for file_path in analyzer_files:
        if not check_file_exists(file_path, f"分析器文件"):
            analyzer_ok = False
    
    # 总结
    print("\n" + "=" * 60)
    print("环境检查总结")
    print("=" * 60)
    
    if file_exists and extractor_ok and kb_ok and analyzer_ok:
        print("✅ 环境准备和初始化完成！所有必需组件都已就绪。")
        print("\n下一步可以执行:")
        print("- 任务2: 逐个错误处理主流程")
        return True
    else:
        print("❌ 环境准备未完成，请检查上述缺失的组件。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
